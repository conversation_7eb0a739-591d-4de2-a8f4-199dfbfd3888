/// Classe base para todas as exceções personalizadas da aplicação
abstract class AppException implements Exception {
  final String message;
  final String? code;
  final dynamic originalException;

  const AppException(this.message, {this.code, this.originalException});

  @override
  String toString() => 'AppException: $message';
}

/// Exceções relacionadas à autenticação
class AuthException extends AppException {
  const AuthException(super.message, {super.code, super.originalException});

  @override
  String toString() => 'AuthException: $message';
}

/// Exceções relacionadas à rede/conectividade
class NetworkException extends AppException {
  const NetworkException(super.message, {super.code, super.originalException});

  @override
  String toString() => 'NetworkException: $message';
}

/// Exceções relacionadas ao Firestore
class FirestoreException extends AppException {
  const FirestoreException(
    super.message, {
    super.code,
    super.originalException,
  });

  @override
  String toString() => 'FirestoreException: $message';
}

/// Exceções relacionadas à validação de dados
class ValidationException extends AppException {
  final String field;

  const ValidationException(
    super.message,
    this.field, {
    super.code,
    super.originalException,
  });

  @override
  String toString() => 'ValidationException [$field]: $message';
}

/// Exceções relacionadas a eventos
class EventException extends AppException {
  const EventException(super.message, {super.code, super.originalException});

  @override
  String toString() => 'EventException: $message';
}

/// Exceções relacionadas a tarefas
class TaskException extends AppException {
  const TaskException(super.message, {super.code, super.originalException});

  @override
  String toString() => 'TaskException: $message';
}

/// Exceções relacionadas a voluntários
class VolunteerException extends AppException {
  const VolunteerException(
    super.message, {
    super.code,
    super.originalException,
  });

  @override
  String toString() => 'VolunteerException: $message';
}

/// Exceções relacionadas a permissões
class PermissionException extends AppException {
  const PermissionException(
    super.message, {
    super.code,
    super.originalException,
  });

  @override
  String toString() => 'PermissionException: $message';
}

/// Exceções relacionadas ao cache/storage
class StorageException extends AppException {
  const StorageException(super.message, {super.code, super.originalException});

  @override
  String toString() => 'StorageException: $message';
}

/// Exceções relacionadas a operações não encontradas
class NotFoundException extends AppException {
  final String resourceType;
  final String resourceId;

  const NotFoundException(
    this.resourceType,
    this.resourceId, {
    String? customMessage,
    super.code,
    super.originalException,
  }) : super(customMessage ?? '$resourceType with ID $resourceId not found');

  @override
  String toString() =>
      'NotFoundException: $resourceType [$resourceId] not found';
}

/// Exceções relacionadas a operações não autorizadas
class UnauthorizedException extends AppException {
  final String action;

  const UnauthorizedException(
    this.action, {
    String? customMessage,
    super.code,
    super.originalException,
  }) : super(customMessage ?? 'Unauthorized to perform action: $action');

  @override
  String toString() => 'UnauthorizedException: $action';
}

/// Exceções relacionadas a conflitos de dados
class ConflictException extends AppException {
  final String resource;

  const ConflictException(
    this.resource, {
    String? customMessage,
    super.code,
    super.originalException,
  }) : super(customMessage ?? 'Conflict with resource: $resource');

  @override
  String toString() => 'ConflictException: $resource';
}

/// Classe utilitária para tratamento de exceções
class ExceptionHandler {
  /// Converte exceções do Firebase Auth em exceções personalizadas
  static AuthException handleAuthException(dynamic exception) {
    if (exception.toString().contains('user-not-found')) {
      return const AuthException(
        'Usuário não encontrado',
        code: 'user-not-found',
      );
    } else if (exception.toString().contains('wrong-password')) {
      return const AuthException('Senha incorreta', code: 'wrong-password');
    } else if (exception.toString().contains('email-already-in-use')) {
      return const AuthException(
        'E-mail já está em uso',
        code: 'email-already-in-use',
      );
    } else if (exception.toString().contains('weak-password')) {
      return const AuthException('Senha muito fraca', code: 'weak-password');
    } else if (exception.toString().contains('invalid-email')) {
      return const AuthException('E-mail inválido', code: 'invalid-email');
    } else if (exception.toString().contains('network-request-failed')) {
      return const AuthException(
        'Erro de conexão',
        code: 'network-request-failed',
      );
    }

    return AuthException('Erro de autenticação', originalException: exception);
  }

  /// Converte exceções do Firestore em exceções personalizadas
  static FirestoreException handleFirestoreException(dynamic exception) {
    if (exception.toString().contains('permission-denied')) {
      return const FirestoreException(
        'Permissão negada',
        code: 'permission-denied',
      );
    } else if (exception.toString().contains('not-found')) {
      return const FirestoreException(
        'Documento não encontrado',
        code: 'not-found',
      );
    } else if (exception.toString().contains('already-exists')) {
      return const FirestoreException(
        'Documento já existe',
        code: 'already-exists',
      );
    } else if (exception.toString().contains('unavailable')) {
      return const FirestoreException(
        'Serviço indisponível',
        code: 'unavailable',
      );
    }

    return FirestoreException(
      'Erro no banco de dados',
      originalException: exception,
    );
  }

  /// Converte exceções de rede em exceções personalizadas
  static NetworkException handleNetworkException(dynamic exception) {
    return NetworkException(
      'Erro de conexão com a internet',
      originalException: exception,
    );
  }

  /// Método genérico para tratar qualquer exceção
  static AppException handleGenericException(dynamic exception) {
    if (exception is AppException) {
      return exception;
    }

    // Retorna uma exceção concreta em vez da classe abstrata
    return NetworkException('Erro inesperado', originalException: exception);
  }
}
