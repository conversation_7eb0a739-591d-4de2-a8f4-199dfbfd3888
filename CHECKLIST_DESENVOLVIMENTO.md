# 📋 Checklist de Desenvolvimento - Task Manager para Eventos

## 🎯 Status Geral do Projeto
- **Plataforma:** Flutter
- **Firebase:** ✅ **CONFIGURADO**
- **Progresso Geral:** 5% (1/20 fases principais)

---

## 📦 Configuração Inicial

### ✅ Configuração do Ambiente
- [x] **Firebase configurado** (Auth, Firestore, Storage)
- [x] **Dependências principais instaladas** (Firebase, Provider, utils)
- [x] **Estrutura de pastas criada** (core/, data/, presentation/)
- [x] **Configuração do tema base** (cores, dimensões, tema Material Design)

### 📁 Estrutura de Pastas
- [x] **`/lib/core/` - Constantes, utils, tema, exceções**
- [x] **`/lib/data/` - Models, repositories, services**
- [x] **`/lib/presentation/` - Controllers, screens, widgets, routes**
- [x] **Arquivos de configuração base**

---

## 🎨 Design System & Tema

### Cores e Estilo
- [x] **Definir cores no `app_colors.dart`**
  - [x] **Cor Principal: Roxo (#6B46C1)**
  - [x] **Cor Secundária: Roxo claro (#A78BFA)**
  - [x] **Cores de fundo, texto, sucesso, erro**
- [x] **Configurar tema Material Design**
- [x] **Criar componentes base (botões, campos, etc.)**

---

## 🔐 FASE 1: Autenticação e Estrutura Base

### Modelos de Dados
- [ ] `user_model.dart` - Modelo do usuário
- [ ] Validações e serialização JSON

### Serviços de Autenticação
- [ ] `auth_service.dart` - Integração Firebase Auth
- [ ] `auth_repository.dart` - Camada de dados
- [ ] `auth_controller.dart` - Gerenciamento de estado

### Telas de Autenticação
- [ ] **Tela de Login**
  - [ ] Layout com logo centralizado
  - [ ] Botão "Entrar com Google"
  - [ ] Link "Criar conta"
- [ ] **Tela de Cadastro**
  - [ ] Campos: nome, email, senha, confirmar senha
  - [ ] Validações (email válido, senha 6+ chars)
  - [ ] Botão "Criar conta"

### Navegação Base
- [ ] Configurar rotas (`app_routes.dart`)
- [ ] Implementar navegação entre telas
- [ ] Proteção de rotas (usuário logado)

---

## 🏠 FASE 2: Tela Home e Gerenciamento de Eventos

### Tela Home
- [ ] **Layout Principal**
  - [ ] AppBar com nome e foto do usuário
  - [ ] Lista de cards dos eventos
  - [ ] FAB com opções "Criar Evento" e "Participar"
- [ ] **Event Card**
  - [ ] Nome do evento
  - [ ] Papel do usuário (Gerenciador/Voluntário)
  - [ ] Número de tarefas pendentes
  - [ ] Status do evento

### Modelos de Eventos
- [ ] `event_model.dart` - Modelo completo do evento
- [ ] `volunteer_profile_model.dart` - Perfil do voluntário

### Serviços de Eventos
- [ ] `event_service.dart` - CRUD de eventos
- [ ] `event_repository.dart` - Camada de dados
- [ ] `event_controller.dart` - Gerenciamento de estado

### Criação de Eventos
- [ ] **Tela Criar Evento**
  - [ ] Formulário completo (nome, descrição, localização)
  - [ ] Seleção de habilidades necessárias
  - [ ] Seleção de recursos necessários
  - [ ] Geração automática de código/tag
- [ ] Sistema de códigos únicos
- [ ] Validações de formulário

### Participação em Eventos
- [ ] **Tela Participar de Evento**
  - [ ] Campo para inserir código/tag
  - [ ] Busca e exibição do evento
  - [ ] Formulário de perfil do voluntário
  - [ ] Seleção de disponibilidade (dias/horários)
  - [ ] Seleção de habilidades e recursos

---

## 📋 FASE 3: Sistema de Tarefas

### Modelos de Tarefas
- [ ] `task_model.dart` - Modelo da task
- [ ] `microtask_model.dart` - Modelo da microtask
- [ ] `user_microtask_model.dart` - Relação usuário-microtask

### Serviços de Tarefas
- [ ] `task_repository.dart` - CRUD de tasks
- [ ] `microtask_repository.dart` - CRUD de microtasks
- [ ] `task_controller.dart` - Gerenciamento de estado
- [ ] `assignment_service.dart` - Sistema de atribuição

### Tela Detalhes do Evento
- [ ] **Sistema de Tabs**
  - [ ] Tab "Evento" - Informações gerais
  - [ ] Tab "Criar Tasks" (apenas gerenciadores)
  - [ ] Tab "Gerenciar Voluntários" (apenas gerenciadores)
  - [ ] Tab "Acompanhar Tasks"

### Criação de Tasks
- [ ] **Tela Criar Tasks**
  - [ ] Seção criar Task (nome, descrição, prioridade)
  - [ ] Seção criar Microtask
  - [ ] Seleção de task pai
  - [ ] Campos específicos da microtask
  - [ ] Validações e persistência

### Gerenciamento de Voluntários
- [ ] **Tela Gerenciar Voluntários**
  - [ ] Lista de voluntários com cards
  - [ ] Indicadores de disponibilidade
  - [ ] Sistema de atribuição de microtasks
  - [ ] Filtro por compatibilidade
  - [ ] Promoção a gerenciador

### Acompanhamento de Tasks
- [ ] **Tela Acompanhar Tasks**
  - [ ] Visualização hierárquica (Tasks → Microtasks)
  - [ ] Status visual e progresso
  - [ ] Filtros (status, prioridade, responsável)
  - [ ] Ações para voluntários (Iniciar/Concluir/Cancelar)

---

## 🧩 Componentes e Widgets

### Widgets Comuns
- [ ] `custom_button.dart` - Botão personalizado
- [ ] `custom_text_field.dart` - Campo de texto
- [ ] `custom_app_bar.dart` - AppBar personalizada
- [ ] `loading_widget.dart` - Indicador de carregamento
- [ ] `error_widget.dart` - Widget de erro
- [ ] `confirmation_dialog.dart` - Dialog de confirmação

### Widgets Específicos
- [ ] `event_card.dart` - Card do evento
- [ ] `task_card.dart` - Card da task
- [ ] `volunteer_card.dart` - Card do voluntário
- [ ] `skill_chip.dart` - Chip de habilidade

---

## 🔧 Funcionalidades Avançadas

### Sistema de Atribuição Inteligente
- [ ] Algoritmo de compatibilidade de habilidades
- [ ] Verificação de disponibilidade de horários
- [ ] Sugestão automática de voluntários
- [ ] Validações de atribuição

### Gerenciamento de Status
- [ ] Sistema de status para eventos
- [ ] Sistema de status para tasks
- [ ] Sistema de status para microtasks
- [ ] Cálculo automático de progresso

---

## 🧪 Testes e Validação

### Testes Funcionais
- [ ] Teste de autenticação
- [ ] Teste de criação de eventos
- [ ] Teste de atribuição de microtasks
- [ ] Teste de fluxo completo

### Validações de Negócio
- [ ] Códigos de evento únicos
- [ ] Verificação de permissões por role
- [ ] Validação de compatibilidade
- [ ] Controle de status
- [ ] Prevenção de atribuição múltipla

---

## 📱 Polimento e Finalização

### UX/UI
- [ ] Revisão de design em todas as telas
- [ ] Animações e transições
- [ ] Feedback visual para ações
- [ ] Tratamento de estados de erro

### Performance
- [ ] Otimização de consultas Firestore
- [ ] Cache de dados
- [ ] Lazy loading de listas
- [ ] Otimização de imagens

---

## 📊 Métricas de Progresso

- **Configuração:** ✅ 4/4 (100%)
- **Fase 1 - Autenticação:** ⏳ 0/12 (0%)
- **Fase 2 - Eventos:** ⏳ 0/15 (0%)
- **Fase 3 - Tarefas:** ⏳ 0/18 (0%)
- **Componentes:** ⏳ 0/10 (0%)
- **Funcionalidades Avançadas:** ⏳ 0/8 (0%)
- **Testes:** ⏳ 0/8 (0%)
- **Polimento:** ⏳ 0/8 (0%)

**PROGRESSO TOTAL: 11/83 tarefas (13.3%)**

---

*Última atualização: 13/07/2025*
*Firebase configurado ✅*
*Dependências instaladas ✅*
*Estrutura de pastas criada ✅*
*Design System configurado ✅*
