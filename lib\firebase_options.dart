// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyC5gDe0YhtXoinP2Zb5NkJBDOBOITzEtck',
    appId: '1:407007104181:web:e47996ce88ac6067d942fc',
    messagingSenderId: '407007104181',
    projectId: 'contask-52156',
    authDomain: 'contask-52156.firebaseapp.com',
    storageBucket: 'contask-52156.firebasestorage.app',
    measurementId: 'G-JY0KQ9T96E',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyByenvoJbzLpxtG8J1omzZ5R1Y4JhVnPK8',
    appId: '1:407007104181:android:5deaa18342a8bb50d942fc',
    messagingSenderId: '407007104181',
    projectId: 'contask-52156',
    storageBucket: 'contask-52156.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDx90MacKn-KCQQYVac0VKEtD6s0HkT24I',
    appId: '1:407007104181:ios:1d95291a2dc4a755d942fc',
    messagingSenderId: '407007104181',
    projectId: 'contask-52156',
    storageBucket: 'contask-52156.firebasestorage.app',
    iosClientId: '407007104181-4rhd2d3mhu8j35rq9ehbuj3r4lov2jva.apps.googleusercontent.com',
    iosBundleId: 'com.example.contask',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyDx90MacKn-KCQQYVac0VKEtD6s0HkT24I',
    appId: '1:407007104181:ios:1d95291a2dc4a755d942fc',
    messagingSenderId: '407007104181',
    projectId: 'contask-52156',
    storageBucket: 'contask-52156.firebasestorage.app',
    iosClientId: '407007104181-4rhd2d3mhu8j35rq9ehbuj3r4lov2jva.apps.googleusercontent.com',
    iosBundleId: 'com.example.contask',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyC5gDe0YhtXoinP2Zb5NkJBDOBOITzEtck',
    appId: '1:407007104181:web:54e10aaa1ec1c479d942fc',
    messagingSenderId: '407007104181',
    projectId: 'contask-52156',
    authDomain: 'contask-52156.firebaseapp.com',
    storageBucket: 'contask-52156.firebasestorage.app',
    measurementId: 'G-EWHDP21GD0',
  );
}
